<?php if (isset($component)) { $__componentOriginaldd64449ebc74a1816c915bc7345cb2f8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaldd64449ebc74a1816c915bc7345cb2f8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.feed-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('feed-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>

    <!-- Create Post Card -->
    <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest border-opacity-20 p-4 mb-4">
        <div class="flex items-center space-x-3">
            <img class="h-10 w-10 rounded-full" src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>" alt="<?php echo e(auth()->user()->name); ?>">
            <div class="flex-1">
                <button onclick="openPersonalPostModal()" class="w-full text-left px-4 py-3 bg-custom-lightest hover:bg-custom-lightest hover:bg-opacity-80 rounded-full text-custom-second-darkest transition-colors">
                    What's on your mind, <?php echo e(auth()->user()->name); ?>?
                </button>
            </div>
        </div>
        <div class="flex items-center justify-between mt-3 pt-3 border-t border-custom-second-darkest border-opacity-20">
            <button onclick="openPersonalPostModal()" class="flex items-center space-x-2 px-4 py-2 text-custom-second-darkest hover:bg-custom-lightest rounded-lg transition-colors">
                <svg class="w-5 h-5 text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm font-medium">Photo/Video</span>
            </button>
            <button onclick="openPersonalPostModal()" class="flex items-center space-x-2 px-4 py-2 text-custom-second-darkest hover:bg-custom-lightest rounded-lg transition-colors">
                <svg class="w-5 h-5 text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm font-medium">Event</span>
            </button>
            <a href="<?php echo e(route('scholarships.create')); ?>" class="flex items-center space-x-2 px-4 py-2 text-custom-second-darkest hover:bg-custom-lightest rounded-lg transition-colors">
                <svg class="w-5 h-5 text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" />
                </svg>
                <span class="text-sm font-medium">Scholarship</span>
            </a>
        </div>
    </div>

    <!-- Filter Pills -->
    <!-- <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest border-opacity-20 p-3 mb-4">
        <div class="flex flex-wrap gap-2">
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-green rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                All Posts
            </button>
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-lightest rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                Events
            </button>
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-lightest rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                Scholarships
            </button>
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-lightest rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                Announcements
            </button>
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-lightest rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                Organizations
            </button>
        </div>
    </div> -->

    <!-- Filter Buttons -->
    <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest border-opacity-20 p-4 mb-6">
        <!-- Search Box -->
        <div class="mb-4">
            <div class="relative">
                <input type="text"
                       id="post-search"
                       placeholder="Search posts..."
                       class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-custom-green focus:border-custom-green">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Search','class' => 'h-5 w-5 text-gray-400']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Search','class' => 'h-5 w-5 text-gray-400']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                </div>
                <button id="search-clear" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 hidden">
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

        <div class="flex flex-wrap items-center gap-3">
            <!-- Filter Label -->
            <span class="text-sm font-medium text-gray-700">Filter by:</span>

            <!-- Post Type Filters -->
            <div class="flex flex-wrap gap-2">
                <button onclick="filterPosts('all')"
                        class="filter-btn active px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="all">
                    All Posts
                </button>
                <button onclick="filterPosts('general')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="general">
                    General
                </button>
                <button onclick="filterPosts('announcement')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="announcement">
                    📢 Announcements
                </button>
                <button onclick="filterPosts('event')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="event">
                    📅 Events
                </button>
                <button onclick="filterPosts('financial_report')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="financial_report">
                    💰 Financial Reports
                </button>
            </div>

            <!-- Separator -->
            <div class="h-6 w-px bg-gray-300"></div>

            <!-- Organization Filters -->
            <div class="flex flex-wrap gap-2">
                <button onclick="filterPosts('personal')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="personal">
                    👤 Personal
                </button>
                <button onclick="filterPosts('organizations')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="organizations">
                    🏢 Organizations
                </button>
                <button onclick="filterPosts('following')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="following">
                    👥 Following
                </button>
            </div>

            <!-- Separator -->
            <div class="h-6 w-px bg-gray-300"></div>

            <!-- Post Method Filters -->
            <div class="flex flex-wrap gap-2">
                <?php
                    $postMethods = \App\Models\PostMethod::active()->get();
                ?>
                <?php $__currentLoopData = $postMethods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <button onclick="filterPosts('post_method_<?php echo e($method->id); ?>')"
                            class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                            data-filter="post_method_<?php echo e($method->id); ?>">
                        <?php echo e($method->name); ?>

                    </button>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Separator -->
            <div class="h-6 w-px bg-gray-300"></div>

            <!-- Tag Filters -->
            <div class="flex flex-wrap gap-2">
                <button onclick="toggleTagFilters()"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        id="tag-filter-toggle">
                    🏷️ Tags
                </button>
            </div>

            <!-- Separator -->
            <div class="h-6 w-px bg-gray-300"></div>

            <!-- Additional Filters -->
            <div class="flex flex-wrap gap-2">
                <button onclick="filterPosts('shared_posts')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="shared_posts">
                    🔄 Shared Posts
                </button>
                <button onclick="filterPosts('group_posts')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="group_posts">
                    👥 Group Posts
                </button>
                <button onclick="filterPosts('with_images')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="with_images">
                    🖼️ With Images
                </button>
                <button onclick="filterPosts('pinned')"
                        class="filter-btn px-3 py-1 rounded-full text-sm font-medium transition-colors border"
                        data-filter="pinned">
                    📌 Pinned
                </button>
            </div>

            <!-- Clear Filters -->
            <button onclick="clearAllFilters()"
                    class="ml-auto px-4 py-1 text-sm text-gray-500 hover:text-gray-700 transition-colors">
                Clear All
            </button>
        </div>

        <!-- Active Filters Display -->
        <div id="active-filters" class="mt-3 hidden">
            <div class="flex items-center gap-2">
                <span class="text-xs text-gray-500">Active filters:</span>
                <div id="active-filters-list" class="flex flex-wrap gap-1"></div>
            </div>
        </div>

        <!-- Tag Filter Dropdown (Hidden by default) -->
        <div id="tag-filters-dropdown" class="hidden mt-4 p-4 bg-gray-50 rounded-lg border">
            <h4 class="text-sm font-medium text-gray-700 mb-3">Filter by Tags</h4>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2" id="tag-filters-container">
                <!-- Tags will be loaded dynamically -->
            </div>
        </div>
    </div>

    <!-- Main Feed -->
    <div class="space-y-6" id="posts-container">
        <?php $__empty_1 = true; $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feedItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <?php if($feedItem->type === 'post'): ?>
                <?php if (isset($component)) { $__componentOriginal14b498b52c33a1421ff8895e4557790f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal14b498b52c33a1421ff8895e4557790f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.post-card','data' => ['post' => $feedItem->data]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('post-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['post' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($feedItem->data)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal14b498b52c33a1421ff8895e4557790f)): ?>
<?php $attributes = $__attributesOriginal14b498b52c33a1421ff8895e4557790f; ?>
<?php unset($__attributesOriginal14b498b52c33a1421ff8895e4557790f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal14b498b52c33a1421ff8895e4557790f)): ?>
<?php $component = $__componentOriginal14b498b52c33a1421ff8895e4557790f; ?>
<?php unset($__componentOriginal14b498b52c33a1421ff8895e4557790f); ?>
<?php endif; ?>
            <?php elseif($feedItem->type === 'share'): ?>
                <?php if (isset($component)) { $__componentOriginald4faf1cbcdbcb738455ac47166667811 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald4faf1cbcdbcb738455ac47166667811 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.shared-post-card','data' => ['share' => $feedItem->data]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shared-post-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['share' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($feedItem->data)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald4faf1cbcdbcb738455ac47166667811)): ?>
<?php $attributes = $__attributesOriginald4faf1cbcdbcb738455ac47166667811; ?>
<?php unset($__attributesOriginald4faf1cbcdbcb738455ac47166667811); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald4faf1cbcdbcb738455ac47166667811)): ?>
<?php $component = $__componentOriginald4faf1cbcdbcb738455ac47166667811; ?>
<?php unset($__componentOriginald4faf1cbcdbcb738455ac47166667811); ?>
<?php endif; ?>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <!-- Empty State -->
            <div id="empty-state" class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10m0 0V6a2 2 0 00-2-2H9a2 2 0 00-2 2v2m10 0v10a2 2 0 01-2 2H9a2 2 0 01-2-2V8m10 0H7m0 0V6a2 2 0 012-2h10a2 2 0 012 2v2M7 8v10a2 2 0 002 2h10a2 2 0 002-2V8" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No posts yet</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating your first post!</p>
                <div class="mt-6">
                    <button onclick="openPersonalPostModal()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-custom-green hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Create Post
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <!-- Pagination -->
        <?php if($posts->hasPages()): ?>
            <div class="pagination-container flex justify-center py-6">
                <nav aria-label="Pagination" class="inline-flex -space-x-px">
                    <?php echo e($posts->links('components.custom-pagination')); ?>

                </nav>
            </div>
        <?php endif; ?>
    </div>

    <!-- Personal Post Creation Modal -->
    <?php echo $__env->make('components.personal-post-creation-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Filter Functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize filter buttons
            initializeFilterButtons();

            // Initialize search functionality
            initializeSearch();
        });

        // Current active filters
        let activeFilters = new Set(['all']);
        let activeTags = new Set();
        let tagsLoaded = false;

        // Initialize filter buttons
        function initializeFilterButtons() {
            // Add active class styling to filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                if (btn.dataset.filter === 'all') {
                    btn.classList.add('bg-custom-green', 'text-white', 'border-custom-green');
                } else {
                    btn.classList.add('bg-white', 'text-gray-700', 'border-gray-300', 'hover:bg-gray-50');
                }
            });

            // Don't apply initial filtering - let server-side rendering show the content
            // Only apply filters when user actually interacts with filters
        }

        // Filter posts based on selected filter
        function filterPosts(filterType) {
            const filterBtn = document.querySelector(`.filter-btn[data-filter="${filterType}"]`);

            // Handle filter button states
            if (filterType === 'all') {
                // Clear all other filters when 'All Posts' is selected
                clearAllFilters();
                activeFilters.add('all');
                updateFilterButtonStates();
            } else {
                // Remove 'all' filter when any other filter is selected
                activeFilters.delete('all');

                // Toggle the selected filter
                if (activeFilters.has(filterType)) {
                    activeFilters.delete(filterType);
                    // If no filters are active, revert to 'all'
                    if (activeFilters.size === 0) {
                        activeFilters.add('all');
                    }
                } else {
                    activeFilters.add(filterType);
                }

                updateFilterButtonStates();
            }

            // Apply the filters
            applyFilters();
        }

        // Update filter button visual states
        function updateFilterButtonStates() {
            document.querySelectorAll('.filter-btn').forEach(btn => {
                const filter = btn.dataset.filter;

                // Reset all button styles
                btn.classList.remove('bg-custom-green', 'text-white', 'border-custom-green', 'active');
                btn.classList.add('bg-white', 'text-gray-700', 'border-gray-300');

                // Apply active styles to active filters
                if (activeFilters.has(filter)) {
                    btn.classList.remove('bg-white', 'text-gray-700', 'border-gray-300');
                    btn.classList.add('bg-custom-green', 'text-white', 'border-custom-green', 'active');
                }
            });

            // Update active filters display
            updateActiveFiltersDisplay();
        }

        // Initialize search functionality
        function initializeSearch() {
            const searchInput = document.getElementById('post-search');
            const searchClear = document.getElementById('search-clear');
            let searchTimeout;

            if (searchInput) {
                // Handle search input
                searchInput.addEventListener('input', function() {
                    // Show/hide clear button
                    if (this.value.length > 0) {
                        searchClear.classList.remove('hidden');
                    } else {
                        searchClear.classList.add('hidden');
                    }

                    // Dynamic search - immediate for first character, debounced for subsequent
                    clearTimeout(searchTimeout);
                    const delay = this.value.length === 1 ? 0 : 150;
                    searchTimeout = setTimeout(() => {
                        applyFilters();
                    }, delay);
                });

                // Handle clear button
                searchClear.addEventListener('click', function() {
                    searchInput.value = '';
                    searchClear.classList.add('hidden');
                    applyFilters();
                });

                // Handle Enter key
                searchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        applyFilters();
                    }
                });
            }
        }

        // Apply filters to posts using AJAX
        function applyFilters() {
            // Show loading state
            const postsContainer = document.getElementById('posts-container');
            postsContainer.classList.add('opacity-50');

            // Build filter parameters
            const params = new URLSearchParams();

            // Add search term if present
            const searchInput = document.getElementById('post-search');
            if (searchInput && searchInput.value.trim().length > 0) {
                params.append('search', searchInput.value.trim());
            }

            // Skip if only 'all' filter is active
            if (!activeFilters.has('all')) {
                // Type filters - support multiple types
                const typeFilters = Array.from(activeFilters).filter(filter =>
                    ['general', 'announcement', 'event', 'financial_report'].includes(filter));

                if (typeFilters.length > 0) {
                    typeFilters.forEach(type => {
                        params.append('types[]', type);
                    });
                }

                // Organization filters - support multiple organization types
                const orgFilters = [];
                if (activeFilters.has('personal')) {
                    orgFilters.push('personal');
                }
                if (activeFilters.has('organizations')) {
                    orgFilters.push('organizations');
                }
                if (orgFilters.length > 0) {
                    orgFilters.forEach(filter => {
                        params.append('organization_filters[]', filter);
                    });
                }

                // Following filter
                if (activeFilters.has('following')) {
                    params.append('following', 'true');
                }

                // Additional filters
                if (activeFilters.has('shared_posts')) {
                    params.append('shared_posts', 'true');
                }

                if (activeFilters.has('group_posts')) {
                    params.append('group_posts', 'true');
                }

                if (activeFilters.has('with_images')) {
                    params.append('with_images', 'true');
                }

                if (activeFilters.has('pinned')) {
                    params.append('pinned', 'true');
                }

                // Post method filters
                activeFilters.forEach(filter => {
                    if (filter.startsWith('post_method_')) {
                        const methodId = filter.replace('post_method_', '');
                        params.append('post_method_id', methodId);
                    }
                });

                // Tag filters
                if (activeTags.size > 0) {
                    activeTags.forEach(tagId => {
                        params.append('tags[]', tagId);
                    });
                }
            }

            // Make AJAX request
            fetch(`/posts-filter?${params.toString()}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update posts container with new HTML
                    postsContainer.innerHTML = data.html;

                    // Update pagination - show if there are multiple pages
                    const paginationContainer = document.querySelector('.pagination-container');
                    if (paginationContainer) {
                        if (data.pagination.last_page > 1) {
                            paginationContainer.innerHTML = data.pagination.links;
                            paginationContainer.classList.remove('hidden');
                        } else {
                            paginationContainer.classList.add('hidden');
                        }
                    }

                    // Show/hide empty state
                    const emptyState = document.getElementById('empty-state');
                    if (emptyState) {
                        if (data.count === 0) {
                            // Clone the empty state from the original HTML if needed
                            if (!postsContainer.contains(emptyState)) {
                                postsContainer.appendChild(emptyState.cloneNode(true));
                            }
                            emptyState.classList.remove('hidden');
                        } else {
                            emptyState.classList.add('hidden');
                        }
                    }

                    // Initialize image modal functionality for new posts
                    initializeImageModals();

                    // Initialize reaction system for new posts
                    if (window.facebookReactionSystem) {
                        window.facebookReactionSystem.createReactionPopups();
                    }
                } else {
                    console.error('Error fetching filtered posts');
                }

                // Remove loading state
                postsContainer.classList.remove('opacity-50');
            })
            .catch(error => {
                console.error('Error:', error);
                postsContainer.classList.remove('opacity-50');

                // Fallback to client-side filtering if AJAX fails
                clientSideFiltering();
            });
        }

        // Client-side filtering fallback
        function clientSideFiltering() {
            const posts = document.querySelectorAll('.post-card');

            posts.forEach(post => {
                const postType = post.dataset.type;
                const hasOrganization = post.dataset.organization !== '';
                const hasImages = post.dataset.hasImages === 'true';
                const isPinned = post.dataset.isPinned === 'true';

                let shouldShow = activeFilters.has('all');

                // Check if post matches any active filter
                if (!shouldShow) {
                    // Type filters
                    if (activeFilters.has(postType)) {
                        shouldShow = true;
                    }

                    // Organization filters
                    if (activeFilters.has('personal') && !hasOrganization) {
                        shouldShow = true;
                    }

                    if (activeFilters.has('organizations') && hasOrganization) {
                        shouldShow = true;
                    }

                    // Additional filters
                    if (activeFilters.has('with_images') && hasImages) {
                        shouldShow = true;
                    }

                    if (activeFilters.has('pinned') && isPinned) {
                        shouldShow = true;
                    }
                }

                // Show or hide post
                if (shouldShow) {
                    post.classList.remove('hidden');
                } else {
                    post.classList.add('hidden');
                }
            });

            // Show empty state if no posts are visible
            const visiblePosts = document.querySelectorAll('.post-card:not(.hidden)');
            const emptyState = document.getElementById('empty-state');

            if (visiblePosts.length === 0 && emptyState) {
                emptyState.classList.remove('hidden');
                // Hide pagination if no posts are visible
                const pagination = document.querySelector('.pagination-container');
                if (pagination) {
                    pagination.classList.add('hidden');
                }
            } else if (emptyState) {
                emptyState.classList.add('hidden');
                // Show pagination if posts are visible
                const pagination = document.querySelector('.pagination-container');
                if (pagination) {
                    pagination.classList.remove('hidden');
                }
            }
        }

        // Initialize image modal functionality for dynamically loaded posts
        function initializeImageModals() {
            // This function would reinitialize any event handlers for the image modal
            // that might be needed for dynamically loaded content
            console.log('Image modals initialized for new content');
        }

        // Update active filters display
        function updateActiveFiltersDisplay() {
            const activeFiltersContainer = document.getElementById('active-filters');
            const activeFiltersList = document.getElementById('active-filters-list');

            // Clear current filters
            activeFiltersList.innerHTML = '';

            // If only 'all' is active, hide the active filters section
            if (activeFilters.has('all') && activeFilters.size === 1) {
                activeFiltersContainer.classList.add('hidden');
                return;
            }

            // Show active filters section
            activeFiltersContainer.classList.remove('hidden');

            // Create filter badges
            activeFilters.forEach(filter => {
                if (filter === 'all') return; // Skip 'all' filter in the display

                const filterName = getFilterDisplayName(filter);
                const badge = document.createElement('span');
                badge.className = 'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-custom-green text-white';
                badge.innerHTML = `
                    ${filterName}
                    <button type="button" onclick="removeFilter('${filter}')" class="ml-1 inline-flex items-center">
                        <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                `;
                activeFiltersList.appendChild(badge);
            });
        }

        // Get display name for filter
        function getFilterDisplayName(filter) {
            const filterNames = {
                'general': 'General',
                'announcement': '📢 Announcements',
                'event': '📅 Events',
                'financial_report': '💰 Financial Reports',
                'personal': '👤 Personal',
                'organizations': '🏢 Organizations',
                'with_images': '🖼️ With Images',
                'pinned': '📌 Pinned'
            };

            return filterNames[filter] || filter;
        }

        // Remove a specific filter
        function removeFilter(filter) {
            activeFilters.delete(filter);

            // If no filters are active, revert to 'all'
            if (activeFilters.size === 0) {
                activeFilters.add('all');
            }

            updateFilterButtonStates();
            applyFilters();
        }

        // Clear all filters
        function clearAllFilters() {
            activeFilters.clear();
            activeFilters.add('all');
            activeTags.clear();
            updateFilterButtonStates();
            updateTagFilterStates();
            applyFilters();
        }

        // Toggle tag filters dropdown
        async function toggleTagFilters() {
            const dropdown = document.getElementById('tag-filters-dropdown');
            const toggle = document.getElementById('tag-filter-toggle');

            if (dropdown.classList.contains('hidden')) {
                // Load tags if not already loaded
                if (!tagsLoaded) {
                    await loadAllTags();
                }
                dropdown.classList.remove('hidden');
                toggle.classList.add('bg-custom-green', 'text-white', 'border-custom-green');
                toggle.classList.remove('bg-white', 'text-gray-700', 'border-gray-300');
            } else {
                dropdown.classList.add('hidden');
                toggle.classList.remove('bg-custom-green', 'text-white', 'border-custom-green');
                toggle.classList.add('bg-white', 'text-gray-700', 'border-gray-300');
            }
        }

        // Load all tags for filtering
        async function loadAllTags() {
            try {
                const postMethods = <?php echo json_encode(\App\Models\PostMethod::with('activeTags')->active()->get(), 15, 512) ?>;
                const container = document.getElementById('tag-filters-container');
                container.innerHTML = '';

                postMethods.forEach(method => {
                    if (method.active_tags && method.active_tags.length > 0) {
                        // Add method header
                        const methodHeader = document.createElement('div');
                        methodHeader.className = 'col-span-full text-xs font-semibold text-gray-600 uppercase tracking-wide mb-1';
                        methodHeader.textContent = method.name;
                        container.appendChild(methodHeader);

                        // Add tags for this method
                        method.active_tags.forEach(tag => {
                            const tagDiv = document.createElement('div');
                            tagDiv.className = 'flex items-center';
                            tagDiv.innerHTML = `
                                <input type="checkbox" id="tag_filter_${tag.id}"
                                       onchange="toggleTagFilter(${tag.id})"
                                       class="h-3 w-3 text-custom-green focus:ring-custom-green border-gray-300 rounded">
                                <label for="tag_filter_${tag.id}" class="ml-2 text-xs text-gray-700 flex items-center cursor-pointer">
                                    <span class="inline-block w-2 h-2 rounded-full mr-1" style="background-color: ${tag.color}"></span>
                                    ${tag.name}
                                </label>
                            `;
                            container.appendChild(tagDiv);
                        });
                    }
                });

                tagsLoaded = true;
            } catch (error) {
                console.error('Error loading tags:', error);
            }
        }

        // Toggle individual tag filter
        function toggleTagFilter(tagId) {
            if (activeTags.has(tagId)) {
                activeTags.delete(tagId);
            } else {
                activeTags.add(tagId);
                // Remove 'all' filter when tags are selected
                activeFilters.delete('all');
                if (activeFilters.size === 0) {
                    activeFilters.add('all');
                }
            }

            updateFilterButtonStates();
            updateTagFilterStates();
            applyFilters();
        }

        // Update tag filter button states
        function updateTagFilterStates() {
            // Update individual tag checkboxes
            activeTags.forEach(tagId => {
                const checkbox = document.getElementById(`tag_filter_${tagId}`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });

            // Clear unchecked tags
            document.querySelectorAll('[id^="tag_filter_"]').forEach(checkbox => {
                const tagId = parseInt(checkbox.id.replace('tag_filter_', ''));
                if (!activeTags.has(tagId)) {
                    checkbox.checked = false;
                }
            });
        }
    </script>

    <!-- Filter Styles -->
    <style>
        .filter-btn {
            transition: all 0.2s ease;
        }
        .filter-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .filter-btn.active {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(123, 199, 77, 0.3);
        }
        #posts-container.opacity-50 {
            transition: opacity 0.3s ease;
        }
        /* Loading animation for filter buttons */
        .filter-btn.loading {
            position: relative;
            color: transparent;
        }
        .filter-btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        /* Responsive filter buttons */
        @media (max-width: 768px) {
            .filter-btn {
                font-size: 12px;
                padding: 6px 12px;
            }
        }
        /* Active filter badges */
        .filter-badge {
            animation: fadeIn 0.3s ease;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.8); }
            to { opacity: 1; transform: scale(1); }
        }
        /* Enhanced Pagination Styles */
        .pagination-container nav {
            display: flex;
            gap: 4px;
        }
        .pagination-container .page-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 36px;
            height: 36px;
            margin: 0 2px;
            padding: 0 12px;
            font-size: 15px;
            font-weight: 500;
            color: #4B5563;
            background: #fff;
            border: 1px solid #E5E7EB;
            border-radius: 9999px;
            transition: all 0.2s;
            cursor: pointer;
        }
        .pagination-container .page-link:hover {
            background: #7BC74D;
            color: #fff;
            border-color: #7BC74D;
        }
        .pagination-container .page-item.active .page-link {
            background: #7BC74D;
            color: #fff;
            border-color: #7BC74D;
            box-shadow: 0 2px 8px rgba(123, 199, 77, 0.15);
        }
        .pagination-container .page-item.disabled .page-link {
            color: #9CA3AF;
            background: #F3F4F6;
            border-color: #E5E7EB;
            cursor: not-allowed;
            opacity: 0.7;
        }
        .pagination-container .page-link:focus {
            outline: none;
            box-shadow: 0 0 0 2px #7BC74D33;
        }
    </style>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaldd64449ebc74a1816c915bc7345cb2f8)): ?>
<?php $attributes = $__attributesOriginaldd64449ebc74a1816c915bc7345cb2f8; ?>
<?php unset($__attributesOriginaldd64449ebc74a1816c915bc7345cb2f8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaldd64449ebc74a1816c915bc7345cb2f8)): ?>
<?php $component = $__componentOriginaldd64449ebc74a1816c915bc7345cb2f8; ?>
<?php unset($__componentOriginaldd64449ebc74a1816c915bc7345cb2f8); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/dashboard.blade.php ENDPATH**/ ?>