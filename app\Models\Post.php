<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Post extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'type',
        'post_method_id',
        'images',
        'facebook_embed_url',
        'is_pinned',
        'status',
        'approval_status',
        'approved_at',
        'approved_by',
        'user_id',
        'organization_id',
        'group_id',
        'published_at',
    ];

    protected $casts = [
        'images' => 'array',
        'is_pinned' => 'boolean',
        'published_at' => 'datetime',
        'approved_at' => 'datetime',
    ];

    /**
     * Get the user who created this post
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the organization this post belongs to
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the group this post belongs to
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    /**
     * Get the user who approved this post
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get all file attachments for this post
     */
    public function fileAttachments(): HasMany
    {
        return $this->hasMany(Attachment::class);
    }

    /**
     * Get all comments for this post
     */
    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    /**
     * Get all reactions for this post
     */
    public function reactions(): MorphMany
    {
        return $this->morphMany(Reaction::class, 'reactable');
    }

    /**
     * Get the post method for this post
     */
    public function postMethod(): BelongsTo
    {
        return $this->belongsTo(PostMethod::class);
    }

    /**
     * Get all tags for this post
     */
    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, 'post_tags');
    }

    /**
     * Get all likes for this post (backward compatibility)
     */
    public function likes(): MorphMany
    {
        return $this->morphMany(Reaction::class, 'reactable')->where('type', 'like');
    }

    /**
     * Check if a user has reacted to this post
     */
    public function isReactedBy($user): bool
    {
        if (!$user) return false;

        return $this->reactions()->where('user_id', $user->id)->exists();
    }

    /**
     * Check if a user has liked this post (backward compatibility)
     */
    public function isLikedBy($user): bool
    {
        if (!$user) return false;

        return $this->reactions()->where('user_id', $user->id)->where('type', 'like')->exists();
    }

    /**
     * Get user's reaction to this post
     */
    public function getUserReaction($user)
    {
        if (!$user) return null;

        return $this->reactions()->where('user_id', $user->id)->first();
    }

    /**
     * Get all shares for this post
     */
    public function shares(): HasMany
    {
        return $this->hasMany(Share::class);
    }



    /**
     * Check if the post is shared by a specific user
     */
    public function isSharedBy(User $user): bool
    {
        return $this->shares()->where('user_id', $user->id)->exists();
    }

    /**
     * Scope for published posts
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope for pinned posts
     */
    public function scopePinned($query)
    {
        return $query->where('is_pinned', true);
    }

    /**
     * Scope for posts by post method
     */
    public function scopeByPostMethod($query, $postMethodId)
    {
        return $query->where('post_method_id', $postMethodId);
    }

    /**
     * Scope for posts with specific tags
     */
    public function scopeWithTags($query, $tagIds)
    {
        if (empty($tagIds)) {
            return $query;
        }

        return $query->whereHas('tags', function ($q) use ($tagIds) {
            $q->whereIn('tags.id', (array) $tagIds);
        });
    }

    /**
     * Sync tags for this post
     */
    public function syncTags($tagIds)
    {
        $this->tags()->sync($tagIds);
    }

    /**
     * Add tags to this post
     */
    public function addTags($tagIds)
    {
        $this->tags()->attach($tagIds);
    }

    /**
     * Remove tags from this post
     */
    public function removeTags($tagIds)
    {
        $this->tags()->detach($tagIds);
    }

    /**
     * Scope for approved posts
     */
    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }

    /**
     * Scope for posts visible in public feeds (excludes pending group posts)
     */
    public function scopeVisibleInFeed($query)
    {
        return $query->where(function($q) {
            // Include posts that don't require approval (personal/org posts)
            $q->whereNull('group_id')
              ->orWhere(function($subQuery) {
                  // Include approved group posts only
                  $subQuery->whereNotNull('group_id')
                           ->where('approval_status', 'approved');
              });
        });
    }

    /**
     * Scope for pending approval posts
     */
    public function scopePendingApproval($query)
    {
        return $query->where('approval_status', 'pending');
    }

    /**
     * Scope for organization posts
     */
    public function scopeOrganizationPosts($query)
    {
        return $query->whereNotNull('organization_id')->whereNull('group_id');
    }

    /**
     * Scope for group posts
     */
    public function scopeGroupPosts($query)
    {
        return $query->whereNotNull('group_id');
    }

    /**
     * Scope for personal posts (not in org or group)
     */
    public function scopePersonalPosts($query)
    {
        return $query->whereNull('organization_id')->whereNull('group_id');
    }

    /**
     * Check if post needs approval
     */
    public function needsApproval(): bool
    {
        return $this->approval_status === 'pending';
    }

    /**
     * Check if post is approved
     */
    public function isApproved(): bool
    {
        return $this->approval_status === 'approved';
    }

    /**
     * Check if post is rejected
     */
    public function isRejected(): bool
    {
        return $this->approval_status === 'rejected';
    }

    /**
     * Get the context (organization or group) this post belongs to
     */
    public function getContext()
    {
        if ($this->group_id) {
            return $this->group;
        }

        if ($this->organization_id) {
            return $this->organization;
        }

        return null;
    }

    /**
     * Get the context type
     */
    public function getContextType(): ?string
    {
        if ($this->group_id) {
            return 'group';
        }

        if ($this->organization_id) {
            return 'organization';
        }

        return 'personal';
    }
}
