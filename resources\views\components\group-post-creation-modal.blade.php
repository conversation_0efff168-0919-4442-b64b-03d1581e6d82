@props(['group' => null, 'organization' => null])

<!-- Group/Organization Post Creation Modal -->
<div id="groupPostCreationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">
                    @if($group)
                        Create Post in {{ $group->name }}
                    @elseif($organization)
                        Create Announcement for {{ $organization->name }}
                    @else
                        Create Post
                    @endif
                </h3>
                <button onclick="closeGroupPostModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <form action="{{ route('posts.store') }}" method="POST" enctype="multipart/form-data" class="p-6 space-y-6">
            @csrf
            
            @if($group)
                <input type="hidden" name="group_id" value="{{ $group->id }}">
            @elseif($organization)
                <input type="hidden" name="organization_id" value="{{ $organization->id }}">
            @endif

            <!-- User Info -->
            <div class="flex items-center space-x-3">
                <img class="h-10 w-10 rounded-full" src="{{ auth()->user()->avatar ? Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7F9CF5&background=EBF4FF' }}" alt="{{ auth()->user()->name }}">
                <div>
                    <p class="text-sm font-medium text-gray-900">{{ auth()->user()->name }}</p>
                    <p class="text-xs text-gray-500">
                        @if($group)
                            Posting to {{ $group->name }}
                            @if($group->post_approval === 'required')
                                <span class="text-yellow-600">(Requires approval)</span>
                            @endif
                        @elseif($organization)
                            Official announcement for {{ $organization->name }}
                        @endif
                    </p>
                </div>
            </div>

            <!-- Tags -->
            @php
                $postMethodSlug = 'user'; // Default
                if ($group) {
                    $postMethodSlug = 'group';
                } elseif ($organization) {
                    $postMethodSlug = 'organization';
                }
                $postMethod = \App\Models\PostMethod::where('slug', $postMethodSlug)->with('activeTags')->first();
            @endphp
            
            @if($postMethod && $postMethod->activeTags->count() > 0)
                <div id="group_modal_tags_section">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Tags</label>
                    <div id="group_modal_tags_container" class="space-y-2 max-h-32 overflow-y-auto">
                        @foreach($postMethod->activeTags as $tag)
                            <div class="flex items-center">
                                <input type="checkbox" name="tags[]" value="{{ $tag->id }}" id="group_modal_tag_{{ $tag->id }}" 
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="group_modal_tag_{{ $tag->id }}" class="ml-2 text-sm text-gray-700 flex items-center">
                                    <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color: {{ $tag->color }}"></span>
                                    {{ $tag->name }}
                                </label>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Title -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title</label>
                <input type="text" name="title" id="title" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="What's your post about?" required>
            </div>

            <!-- Content -->
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-1">Content</label>
                <textarea name="content" id="content" rows="4" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Share your thoughts..." required></textarea>
            </div>

            <!-- Image Upload -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Images</label>
                <x-file-upload-zone
                    name="images"
                    accept="image/*"
                    :multiple="true"
                    max-size="10MB"
                    title="Upload Images"
                    description="Drag and drop images here or click to browse" />
            </div>

            <!-- File Upload -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">File Attachments</label>
                @if($group && $group->allow_file_sharing)
                    <!-- Group post attachments with group-specific settings -->
                    <x-file-upload-zone
                        name="attachments"
                        accept="{{ $group->allowed_file_types ? '.' . implode(',.', $group->allowed_file_types) : '*' }}"
                        :multiple="true"
                        max-size="{{ $group->max_file_size_mb }}MB"
                        :allowed-types="$group->allowed_file_types"
                        title="Upload Files"
                        description="Drag and drop files here or click to browse" />
                @elseif($group && !$group->allow_file_sharing)
                    <!-- Group doesn't allow file sharing -->
                    <div class="text-sm text-gray-500 italic">
                        File attachments are not allowed in this group.
                    </div>
                @else
                    <!-- Default file upload for organizations or other contexts -->
                    <x-file-upload-zone
                        name="attachments"
                        accept="*"
                        :multiple="true"
                        max-size="50MB"
                        title="Upload Files"
                        description="Drag and drop files here or click to browse" />
                @endif
            </div>

            <!-- Facebook Embed URL -->
            <div>
                <label for="facebook_embed_url" class="block text-sm font-medium text-gray-700 mb-1">Facebook Post URL (Optional)</label>
                <input type="url" name="facebook_embed_url" id="facebook_embed_url" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="https://www.facebook.com/...">
            </div>

            <!-- Status -->
            <input type="hidden" name="status" value="published">

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <button type="button" onclick="closeGroupPostModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-400">
                    Cancel
                </button>
                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700">
                    @if($group && $group->post_approval === 'required')
                        Submit for Approval
                    @else
                        Post
                    @endif
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    function openGroupPostModal() {
        document.getElementById('groupPostCreationModal').classList.remove('hidden');
        document.getElementById('groupPostCreationModal').classList.add('flex');
        document.body.style.overflow = 'hidden';
    }

    function closeGroupPostModal() {
        document.getElementById('groupPostCreationModal').classList.add('hidden');
        document.getElementById('groupPostCreationModal').classList.remove('flex');
        document.body.style.overflow = 'auto';

        // Reset form
        document.querySelector('#groupPostCreationModal form').reset();

        // Reset file upload zones
        const imagePreviews = document.querySelectorAll('#groupPostCreationModal [id$="-preview"]');
        imagePreviews.forEach(preview => {
            preview.classList.add('hidden');
            preview.innerHTML = '';
        });

        // Reset tag checkboxes
        const tagCheckboxes = document.querySelectorAll('#group_modal_tags_container input[type="checkbox"]');
        tagCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
    }

    // Tags are now loaded automatically based on context, no JavaScript needed
</script>
